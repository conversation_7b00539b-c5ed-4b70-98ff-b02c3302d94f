import { useKeycloak } from '@react-keycloak/web';
import {
  createBrowserRouter,
  Navigate,
  RouterProvider,
  Outlet,
} from 'react-router-dom';

import { MainLoaderSkeleton } from '../components/hocs/suspense/withSuspense';
import DashboardNavigationShell from '../components/ui/template/DashboardNavigationShell';
import NonAuthNavigationShell from '../components/ui/template/NonAuthNavigationShell';
import UserOnboardingNavigationShell from '../components/ui/template/UserOnboardingNavigationShell';
import WebsiteNavigationShell from '../components/ui/template/WebsiteNavigationShell';
import { PivotlNavigationShell } from '../apps/pivoTL/components/layout/PivotlNavigationShell';
import { DashboardShellRoutes } from './DashboardShellRoutes';
import { NonAuthShellRoutes } from './NonAuthShellRoutes';
import { UserOnboardingShellRoutes } from './UserOnboardingShellRoutes';
import { WebsiteShellRoutes } from './WebsiteShellRoutes';
import { PivotlShellRoutes } from '../apps/pivoTL/routes/PivotlShellRoutes';
import { useNetworkRefetch } from '@/hooks/useNetworkRefetch';
import { isDevEnvironment } from '@/features/UserOnboarding/utils/helper';
import { DynamicHead } from '@/shared/components/layout/DynamicHead';

const RootLayout = () => (
  <>
    <DynamicHead />
    <Outlet />
  </>
);

const router = createBrowserRouter([
  {
    element: <RootLayout />,
    children: [
      {
        path: '/',
        element: <WebsiteNavigationShell />,
        children: WebsiteShellRoutes,
      },
      {
        path: '/',
        element: <DashboardNavigationShell />,
        children: DashboardShellRoutes,
      },
      {
        path: '/',
        element: <NonAuthNavigationShell />,
        children: NonAuthShellRoutes,
      },
      {
        path: '/',
        element: <UserOnboardingNavigationShell />,
        children: UserOnboardingShellRoutes,
      },
      {
        path: '/pivotl',
        element: <PivotlNavigationShell />,
        children: PivotlShellRoutes,
        errorElement: <PivotlNavigationShell />,
      },
      {
        path: '*',
        element: <Navigate to="/" />,
      },
    ],
  },
]);

export const MainAppRoutes = () => {
  useNetworkRefetch();
  const { initialized } = useKeycloak();
  if (!initialized && !isDevEnvironment()) return <MainLoaderSkeleton />;
  return <RouterProvider router={router} />;
};

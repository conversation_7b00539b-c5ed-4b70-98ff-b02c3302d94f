import { useState } from 'react';
import { <PERSON>, EyeOff, Check, X } from 'lucide-react';
import { OnboardingProgress, PasswordValidation } from '../../types/chat';
import { halfEllipse } from '../../assets/images';
import { VerificationCodeInput } from '../chat/VerificationCodeInput';

interface OnboardingProgressSidebarProps {
  onboardingProgress: OnboardingProgress;
  onResendVerificationCode: () => void;
}

export const OnboardingProgressSidebar = ({
  onboardingProgress,
  onResendVerificationCode,
}: OnboardingProgressSidebarProps) => {
  const [showPassword, setShowPassword] = useState(false);
  const [verificationCode, setVerificationCode] = useState('');

  const validatePassword = (password: string): PasswordValidation => {
    return {
      minLength: password.length >= 8,
      hasLowercase: /[a-z]/.test(password),
      hasUppercase: /[A-Z]/.test(password),
      hasDigit: /\d/.test(password),
      hasSpecialChar: /[!@#$%^&*(),.?":{}|<>]/.test(password),
    };
  };

  console.log('onboardingProgress', onboardingProgress);

  const passwordValidation = onboardingProgress.password
    ? validatePassword(onboardingProgress.password)
    : {
        minLength: false,
        hasLowercase: false,
        hasUppercase: false,
        hasDigit: false,
        hasSpecialChar: false,
      };

  const ValidationIcon = ({ isValid }: { isValid: boolean }) =>
    isValid ? (
      <Check className="text-green-500 h-4 w-4" />
    ) : (
      <X className="h-4 w-4 text-red-500" />
    );

  function handleVerificationSubmit(code: string) {
    console.log(verificationCode);
    // Send Scyra the verification code. For now, we'll just log it and simulate a success/failure.
    if (code.length === 6 && /^\d{6}$/.test(code)) {
      // Simulate successful verification
      console.log('Verification code submitted:', code);
      // You could update onboardingProgress to password step or trigger a callback here
      // e.g., setOnboardingProgress({ ...onboardingProgress, verified: true });
    } else {
      // Simulate error
      console.error('Invalid verification code:', code);
      // Optionally show an error message to the user
    }
  }

  return (
    <div className="sticky top-8 flex h-full flex-col justify-center rounded-lg bg-gradient-to-br from-white to-orange-50 font-inter">
      {/* Background Grid */}
      <div
        className="absolute inset-0 z-0 -mt-40"
        style={{
          backgroundImage: `url(${halfEllipse})`,
          backgroundSize: 'auto',
          backgroundPosition: 'right',
          backgroundRepeat: 'no-repeat',
        }}
      />
      <div className="z-10 mx-3 rounded bg-white bg-opacity-50 p-6">
        <div className="flex justify-center">
          <h3 className="mb-6 text-lg font-medium text-darkGray">Sign Up</h3>
        </div>

        <div className="space-y-6">
          {/* First Name */}
          {onboardingProgress.firstName && (
            <div>
              <div className="rounded-md border border-grayFifteen bg-gray-50 px-3 py-2 text-sm text-blackTwo">
                {onboardingProgress.firstName}
              </div>
            </div>
          )}

          {/* Last Name */}
          {onboardingProgress.lastName && (
            <div>
              <div className="rounded-md border border-grayFifteen bg-gray-50 px-3 py-2 text-sm text-blackTwo">
                {onboardingProgress.lastName}
              </div>
            </div>
          )}

          {/* Email */}
          {onboardingProgress.email && (
            <div>
              <div className="rounded-md border border-grayFifteen bg-gray-50 px-3 py-2 text-sm text-blackTwo">
                {onboardingProgress.email}
              </div>
            </div>
          )}

          {/* Verification Code */}
          {onboardingProgress.currentStep === 'verification' && (
            <div className="rounded bg-gray-100 p-4">
              <VerificationCodeInput
                email={onboardingProgress.email}
                onResend={onResendVerificationCode}
                onComplete={code => {
                  setVerificationCode(code);
                  // Auto-submit when complete
                  handleVerificationSubmit(code);
                }}
              />
            </div>
          )}

          {/* Password */}
          {onboardingProgress.currentStep === 'password' && (
            <div>
              <label className="mb-1 block text-sm font-medium text-darkGray">
                Enter password
              </label>
              <div className="relative">
                <input
                  type={showPassword ? 'text' : 'password'}
                  value={onboardingProgress.password || ''}
                  readOnly
                  className="w-full rounded-md border border-grayFifteen px-3 py-2 pr-10 text-sm focus:border-primary focus:outline-none"
                  placeholder="Password"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-primary"
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </button>
              </div>

              {/* Password Validation Rules */}
              <div className="mt-2 space-y-1 text-xs">
                <div className="flex items-center gap-2">
                  <ValidationIcon isValid={passwordValidation.minLength} />
                  <span
                    className={
                      passwordValidation.minLength
                        ? 'text-green-600'
                        : 'text-red-600'
                    }
                  >
                    At least 8 characters long
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <ValidationIcon isValid={passwordValidation.hasLowercase} />
                  <span
                    className={
                      passwordValidation.hasLowercase
                        ? 'text-green-600'
                        : 'text-red-600'
                    }
                  >
                    At least one lowercase letter
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <ValidationIcon isValid={passwordValidation.hasUppercase} />
                  <span
                    className={
                      passwordValidation.hasUppercase
                        ? 'text-green-600'
                        : 'text-red-600'
                    }
                  >
                    At least one uppercase letter
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <ValidationIcon isValid={passwordValidation.hasDigit} />
                  <span
                    className={
                      passwordValidation.hasDigit
                        ? 'text-green-600'
                        : 'text-red-600'
                    }
                  >
                    At least one digit
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <ValidationIcon isValid={passwordValidation.hasSpecialChar} />
                  <span
                    className={
                      passwordValidation.hasSpecialChar
                        ? 'text-green-600'
                        : 'text-red-600'
                    }
                  >
                    At least one special character
                  </span>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

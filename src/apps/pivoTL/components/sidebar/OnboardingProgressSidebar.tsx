import { useState } from 'react';
import { <PERSON>, EyeOff, Check, X } from 'lucide-react';
import { OnboardingProgress, PasswordValidation } from '../../types/chat';

interface OnboardingProgressSidebarProps {
  onboardingProgress: OnboardingProgress;
  onResendVerificationCode: () => void;
}

export const OnboardingProgressSidebar = ({
  onboardingProgress,
  onResendVerificationCode,
}: OnboardingProgressSidebarProps) => {
  const [showPassword, setShowPassword] = useState(false);
  const [verificationCode, setVerificationCode] = useState('');

  const validatePassword = (password: string): PasswordValidation => {
    return {
      minLength: password.length >= 8,
      hasLowercase: /[a-z]/.test(password),
      hasUppercase: /[A-Z]/.test(password),
      hasDigit: /\d/.test(password),
      hasSpecialChar: /[!@#$%^&*(),.?":{}|<>]/.test(password),
    };
  };

  const passwordValidation = onboardingProgress.password
    ? validatePassword(onboardingProgress.password)
    : {
        minLength: false,
        hasLowercase: false,
        hasUppercase: false,
        hasDigit: false,
        hasSpecialChar: false,
      };

  const ValidationIcon = ({ isValid }: { isValid: boolean }) =>
    isValid ? (
      <Check className="text-green-500 h-4 w-4" />
    ) : (
      <X className="h-4 w-4 text-red-500" />
    );

  return (
    <div className="sticky top-8 rounded-lg">
      <h3 className="mb-4 text-lg font-semibold text-darkGray">Sign Up</h3>

      <div className="space-y-4">
        {/* First Name */}
        {onboardingProgress.firstName && (
          <div>
            <label className="mb-1 block text-sm font-medium text-darkGray">
              First Name
            </label>
            <div className="rounded-md border border-grayNine bg-gray-50 px-3 py-2 text-sm text-blackTwo">
              {onboardingProgress.firstName}
            </div>
          </div>
        )}

        {/* Last Name */}
        {onboardingProgress.lastName && (
          <div>
            <label className="mb-1 block text-sm font-medium text-darkGray">
              Last Name
            </label>
            <div className="rounded-md border border-grayNine bg-gray-50 px-3 py-2 text-sm text-blackTwo">
              {onboardingProgress.lastName}
            </div>
          </div>
        )}

        {/* Email */}
        {onboardingProgress.email && (
          <div>
            <label className="mb-1 block text-sm font-medium text-darkGray">
              Email
            </label>
            <div className="rounded-md border border-grayNine bg-gray-50 px-3 py-2 text-sm text-blackTwo">
              {onboardingProgress.email}
            </div>
          </div>
        )}

        {/* Verification Code */}
        {onboardingProgress.currentStep === 'verification' && (
          <div>
            <label className="mb-1 block text-sm font-medium text-darkGray">
              Enter 6-digit code sent to {onboardingProgress.email}
            </label>
            <input
              type="text"
              value={verificationCode}
              onChange={e => setVerificationCode(e.target.value)}
              className="w-full rounded-md border border-grayNine px-3 py-2 text-sm focus:border-primary focus:outline-none"
              placeholder="000000"
              maxLength={6}
            />
            <button
              onClick={onResendVerificationCode}
              className="mt-2 text-sm text-primary underline hover:text-orange-15"
            >
              Didn't get a message? Resend Code
            </button>
          </div>
        )}

        {/* Password */}
        {onboardingProgress.currentStep === 'password' && (
          <div>
            <label className="mb-1 block text-sm font-medium text-darkGray">
              Enter password
            </label>
            <div className="relative">
              <input
                type={showPassword ? 'text' : 'password'}
                value={onboardingProgress.password || ''}
                readOnly
                className="w-full rounded-md border border-grayNine px-3 py-2 pr-10 text-sm focus:border-primary focus:outline-none"
                placeholder="Password"
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-primary"
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </button>
            </div>

            {/* Password Validation Rules */}
            <div className="mt-2 space-y-1 text-xs">
              <div className="flex items-center gap-2">
                <ValidationIcon isValid={passwordValidation.minLength} />
                <span
                  className={
                    passwordValidation.minLength
                      ? 'text-green-600'
                      : 'text-red-600'
                  }
                >
                  At least 8 characters long
                </span>
              </div>
              <div className="flex items-center gap-2">
                <ValidationIcon isValid={passwordValidation.hasLowercase} />
                <span
                  className={
                    passwordValidation.hasLowercase
                      ? 'text-green-600'
                      : 'text-red-600'
                  }
                >
                  At least one lowercase letter
                </span>
              </div>
              <div className="flex items-center gap-2">
                <ValidationIcon isValid={passwordValidation.hasUppercase} />
                <span
                  className={
                    passwordValidation.hasUppercase
                      ? 'text-green-600'
                      : 'text-red-600'
                  }
                >
                  At least one uppercase letter
                </span>
              </div>
              <div className="flex items-center gap-2">
                <ValidationIcon isValid={passwordValidation.hasDigit} />
                <span
                  className={
                    passwordValidation.hasDigit
                      ? 'text-green-600'
                      : 'text-red-600'
                  }
                >
                  At least one digit
                </span>
              </div>
              <div className="flex items-center gap-2">
                <ValidationIcon isValid={passwordValidation.hasSpecialChar} />
                <span
                  className={
                    passwordValidation.hasSpecialChar
                      ? 'text-green-600'
                      : 'text-red-600'
                  }
                >
                  At least one special character
                </span>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

import {
  useState,
  useRef,
  useEffect,
  KeyboardEvent,
  ClipboardEvent,
} from 'react';

interface VerificationCodeInputProps {
  email?: string;
  onResend: () => void;
  onComplete: (code: string) => void;
}

export const VerificationCodeInput = ({
  email,
  onResend,
  onComplete,
}: VerificationCodeInputProps) => {
  const [codes, setCodes] = useState<string[]>(Array(6).fill(''));
  const inputRefs = useRef<(HTMLInputElement | null)[]>(Array(6).fill(null));

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    index: number,
  ) => {
    const value = e.target.value;

    // Only allow single digit input
    if (/^\d*$/.test(value) && value.length <= 1) {
      const newCodes = [...codes];
      newCodes[index] = value;
      setCodes(newCodes);

      // Auto-focus next input if a digit was entered
      if (value && index < 5 && inputRefs.current[index + 1]) {
        inputRefs.current[index + 1]?.focus();
      }

      // Check if all codes are filled
      if (newCodes.every(code => code !== '') && index === 5) {
        onComplete(newCodes.join(''));
      }
    }
  };

  const handlePaste = (e: ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    const pasteData = e.clipboardData.getData('text/plain').slice(0, 6);

    if (/^\d{6}$/.test(pasteData)) {
      const newCodes = pasteData.split('');
      setCodes(newCodes);

      // Focus the last input
      inputRefs.current[5]?.focus();
      onComplete(newCodes.join(''));
    }
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>, index: number) => {
    // Move focus to previous input on backspace if current is empty
    if (e.key === 'Backspace' && !codes[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  // Auto-focus first input on mount
  useEffect(() => {
    inputRefs.current[0]?.focus();
  }, []);

  return (
    <div>
      <label className="mb-4 block text-center text-gray-700">
        {email
          ? `Enter the 6-digit code sent to ${email}`
          : 'Enter the 6-digit code sent to your email'}
      </label>

      <div className="flex justify-center space-x-2">
        {codes.map((code, index) => (
          <input
            key={index}
            ref={el => (inputRefs.current[index] = el)}
            type="text"
            value={code}
            onChange={e => handleChange(e, index)}
            onPaste={handlePaste}
            onKeyDown={e => handleKeyDown(e, index)}
            className="text-blackTwo h-10 w-10 rounded-lg border border-gray-300 text-center text-lg font-medium transition-all focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20"
            maxLength={1}
            inputMode="numeric"
            pattern="[0-9]*"
          />
        ))}
      </div>

      <div className='flex justify-center'>
        <button
          onClick={onResend}
          className="hover:text-primary-dark mt-4 text-sm text-gray-700 transition-colors"
        >
          Didn't get a message?{' '}
          <span className="hover:text-primary-dark text-primary underline">
            Resend Code
          </span>
        </button>
      </div>
    </div>
  );
};

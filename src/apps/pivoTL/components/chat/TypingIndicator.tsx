import { scyra } from '../../assets/images';

export const TypingIndicator = () => {
  return (
    <div className="mb-4 flex gap-3">
      {/* Scyra Avatar */}
      <div className="h-10 w-10 flex-shrink-0">
        <img
          src={scyra}
          alt="Scyra"
          className="h-full w-full rounded-full object-cover"
        />
      </div>

      {/* Typing Content */}
      <div className="flex-1">
        {/* Header */}
        <div className="mb-1 flex items-center gap-2">
          <span className="font-semibold text-darkGray">Scyra</span>
        </div>

        {/* Typing Animation */}
        <div className="flex items-center gap-1 text-sm text-gray-500">
          <span><PERSON><PERSON> is typing</span>
          <div className="flex gap-1">
            <div className="h-1 w-1 animate-bounce rounded-full bg-gray-400 [animation-delay:-0.3s]"></div>
            <div className="h-1 w-1 animate-bounce rounded-full bg-gray-400 [animation-delay:-0.15s]"></div>
            <div className="h-1 w-1 animate-bounce rounded-full bg-gray-400"></div>
          </div>
        </div>
      </div>
    </div>
  );
};

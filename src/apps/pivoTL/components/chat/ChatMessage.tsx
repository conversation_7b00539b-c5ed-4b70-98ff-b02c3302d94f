import { ChatMessage as ChatMessageType } from '../../types/chat';
import { scyra } from '../../assets/images';
import { User } from 'lucide-react';
import { Helper } from '../../../../utils/helpers';

interface ChatMessageProps {
  message: ChatMessageType;
}

export const ChatMessage = ({ message }: ChatMessageProps) => {
  const isUser = message.sender === 'user';
  // const isScyra = message.sender === 'scyra';

  return (
    <div className="mb-4 flex gap-3">
      {/* Avatar */}
      <div className="h-10 w-10 flex-shrink-0">
        {isUser ? (
          <div className="flex h-full w-full items-center justify-center rounded-full bg-gray-200">
            <User className="h-5 w-5 text-gray-600" />
          </div>
        ) : (
          <div className="rounded-full bg-peachTwo">
            <img
              src={scyra}
              alt="Scyra"
              className="h-full w-full rounded-full object-cover"
            />
          </div>
        )}
      </div>

      {/* Message Content */}
      <div className="flex-1">
        {/* Header */}
        <div className="mb-1 flex items-center gap-2">
          <span className="font-semibold text-darkGray">
            {message.senderName}
          </span>
          <span className="text-xs text-gray-500">
            {Helper.getDateFormNow(message.timestamp)}
          </span>
        </div>

        {/* Message Text */}
        <div className="rounded bg-gray-5 p-3 text-grayTwentyFour">
          {message.content}
        </div>
      </div>
    </div>
  );
};

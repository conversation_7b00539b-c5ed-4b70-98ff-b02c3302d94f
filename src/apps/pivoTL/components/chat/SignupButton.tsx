import { FolderO<PERSON>, Mail } from 'lucide-react';

interface SignupButtonProps {
  onSignupStart: () => void;
  disabled?: boolean;
}

export const SignupButton = ({
  onSignupStart,
  disabled = false,
}: SignupButtonProps) => {
  return (
    <div className="mt-3 flex justify-start gap-2 text-gray-600">
      <button
        onClick={onSignupStart}
        disabled={disabled}
        className="flex items-center gap-2 rounded-md border border-gray-400 bg-white px-3 py-1 text-sm font-medium transition-colors hover:border-primary hover:bg-lightOrangeTwo disabled:cursor-not-allowed disabled:opacity-50"
      >
        <Mail className="w-4" />
        <span>Proceed to Sign Up</span>
      </button>

      <button
        // onClick={onSignupStart}
        disabled={disabled}
        className="flex items-center gap-2 rounded-md border border-gray-400 bg-white px-3 py-1 text-sm font-medium transition-colors hover:border-primary hover:bg-lightOrangeTwo disabled:cursor-not-allowed disabled:opacity-50"
      >
        <FolderOpen className="w-4" />
        <span>Connect CRM</span>
      </button>

      <button
        // onClick={onSignupStart}
        disabled={disabled}
        className="flex items-center gap-2 rounded-md border border-gray-400 bg-white px-3 py-1 text-sm font-medium transition-colors hover:border-primary hover:bg-lightOrangeTwo disabled:cursor-not-allowed disabled:opacity-50"
      >
        <Mail className="w-4" />
        <span>Connect Email</span>
      </button>

      <button
        // onClick={onSignupStart}
        disabled={disabled}
        className="flex items-center gap-2 rounded-md border border-gray-400 bg-white px-3 py-1 text-sm font-medium transition-colors hover:border-primary hover:bg-lightOrangeTwo disabled:cursor-not-allowed disabled:opacity-50"
      >
        <Mail className="w-4" />
        <span>Connect SMS Provider</span>
      </button>
    </div>
  );
};

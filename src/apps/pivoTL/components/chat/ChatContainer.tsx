import { useState } from 'react';
import { ConversationalChatInterface } from './ConversationalChatInterface';
import { MarketplaceAgentsSidebar } from '../sidebar/MarketplaceAgentsSidebar';
import { OnboardingProgressSidebar } from '../sidebar/OnboardingProgressSidebar';
import { marketplaceAgents } from '../../data/constants';
import { useChatApi } from '../../hooks/useChatApi';

export const ChatContainer = () => {
  const [activeAgent, setActiveAgent] = useState(marketplaceAgents[0]);
  const {
    state,
    sendMessage,
    handleSignupStart,
    handleResendVerificationCode,
  } = useChatApi();

  const showOnboardingProgress = state.onboardingProgress.isSignupStarted;

  return (
    <div className="grid grid-cols-1 gap-8 font-inter lg:grid-cols-3">
      {/* Main Chat Content */}
      <div className="lg:col-span-2">
        <div className="h-[600px] bg-white">
          <ConversationalChatInterface
            state={state}
            sendMessage={sendMessage}
            handleSignupStart={handleSignupStart}
          />
        </div>
      </div>

      {/* Sidebar */}
      <div className="lg:col-span-1">
        {showOnboardingProgress ? (
          <OnboardingProgressSidebar
            onboardingProgress={state.onboardingProgress}
            onResendVerificationCode={handleResendVerificationCode}
          />
        ) : (
          <MarketplaceAgentsSidebar
            activeAgent={activeAgent}
            onAgentSelect={setActiveAgent}
          />
        )}
      </div>
    </div>
  );
};

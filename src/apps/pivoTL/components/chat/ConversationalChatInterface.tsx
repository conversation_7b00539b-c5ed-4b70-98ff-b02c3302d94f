import { useEffect, useRef } from 'react';
import { ChatMessage } from './ChatMessage';
import { TypingIndicator } from './TypingIndicator';
import { ChatInput } from './ChatInput';
import { SignupButton } from './SignupButton';
import { ProceedToPivoButton } from './ProceedToPivoButton';
import { useChatApi } from '../../hooks/useChatApi';

export const ConversationalChatInterface = () => {
  const {
    state,
    sendMessage,
    handleSignupStart,
    // handleResendVerificationCode,
  } = useChatApi();
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [state.messages, state.isLoading]);

  const handleProceedToPivo = () => {
    // Navigate to main Pivo application
    window.location.href = '/';
  };

  const getLastScyraMessage = () => {
    const scyraMessages = state.messages.filter(msg => msg.sender === 'scyra');
    return scyraMessages[scyraMessages.length - 1];
  };

  const lastScyraMessage = getLastScyraMessage();

  return (
    <div className="flex h-[85%] flex-col">
      {/* Messages Container */}
      <div className="flex-1 overflow-y-auto px-4 py-4">
        {state.messages.map(message => (
          <div key={message.id}>
            <ChatMessage message={message} />

            {/* Show signup button only after the most recent Scyra message */}
            {message.sender === 'scyra' &&
              message.id === lastScyraMessage?.id &&
              state.showSignupButton && (
                <div className="ml-[52px]">
                  <SignupButton
                    onSignupStart={handleSignupStart}
                    disabled={state.isLoading}
                  />
                </div>
              )}

            {/* Show proceed to Pivo button after signup completion */}
            {message.sender === 'scyra' &&
              message.id === lastScyraMessage?.id &&
              state.showProceedToPivoButton && (
                <ProceedToPivoButton
                  onProceedToPivo={handleProceedToPivo}
                  disabled={state.isLoading}
                />
              )}
          </div>
        ))}

        {/* Typing Indicator */}
        {state.isLoading && <TypingIndicator />}

        <div ref={messagesEndRef} />
      </div>

      {/* Chat Input */}
      <div className="px-4 py-4">
        <ChatInput onSendMessage={sendMessage} disabled={state.isLoading} />
      </div>
    </div>
  );
};

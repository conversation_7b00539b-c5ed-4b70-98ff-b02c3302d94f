import { useEffect, useRef } from 'react';
import { ChatMessage } from './ChatMessage';
import { TypingIndicator } from './TypingIndicator';
import { ChatInput } from './ChatInput';
import { SignupButton } from './SignupButton';
import { ProceedToPivoButton } from './ProceedToPivoButton';
import { ConversationState } from '../../types/chat';

interface ConversationalChatInterfaceProps {
  state: ConversationState;
  sendMessage: (userMessage: string) => Promise<void>;
  handleSignupStart: () => Promise<void>;
}

export const ConversationalChatInterface = ({
  state,
  sendMessage,
  handleSignupStart,
}: ConversationalChatInterfaceProps) => {
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTop =
        messagesContainerRef.current.scrollHeight;
    }
  };

  useEffect(() => {
    scrollToBottom();
  }, [state.messages, state.isLoading]);

  const handleProceedToPivo = () => {
    // Navigate to main Pivo application
    window.location.href = '/pivotl/dashboard';
  };

  const getLastScyraMessage = () => {
    const scyraMessages = state.messages.filter(msg => msg.sender === 'scyra');
    return scyraMessages[scyraMessages.length - 1];
  };

  const lastScyraMessage = getLastScyraMessage();

  return (
    <div className="flex h-[95%] flex-col">
      {/* Messages Container */}
      <div
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto px-4 py-4"
      >
        {state.messages.map(message => (
          <div key={message.id}>
            <ChatMessage message={message} />

            {/* Show signup button only after the most recent Scyra message */}
            {message.sender === 'scyra' &&
              message.id === lastScyraMessage?.id &&
              state.showSignupButton && (
                <div className="ml-[52px]">
                  <SignupButton
                    onSignupStart={handleSignupStart}
                    disabled={state.isLoading}
                  />
                </div>
              )}

            {/* Show proceed to Pivo button after signup completion */}
            {message.sender === 'scyra' &&
              message.id === lastScyraMessage?.id &&
              state.showProceedToPivoButton && (
                <div className="ml-12">
                  <ProceedToPivoButton
                    onProceedToPivo={handleProceedToPivo}
                    disabled={state.isLoading}
                  />
                </div>
              )}
          </div>
        ))}

        {/* Typing Indicator */}
        {state.isLoading && <TypingIndicator />}
      </div>

      {/* Chat Input */}
      <div className="px-4 py-4">
        <ChatInput onSendMessage={sendMessage} disabled={state.isLoading} />
      </div>
    </div>
  );
};

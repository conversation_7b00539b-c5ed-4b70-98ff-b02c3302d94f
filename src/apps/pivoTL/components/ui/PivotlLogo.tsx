import { Link } from 'react-router-dom';
import { pivotlLogo } from '../../assets/icons';

interface PivotlLogoProps {
  variant?: 'dark' | 'light';
}

const PivotlLogo = ({ variant = 'dark' }: PivotlLogoProps) => {
  const textColor = variant === 'dark' ? 'text-gray-900' : 'text-white';

  return (
    <Link to="/pivotl">
      <div className="flex items-center space-x-2">
        <img loading="lazy" src={pivotlLogo} />
        <span className={`font-space-mono text-3xl font-bold ${textColor}`}>
          PivoTL
        </span>
      </div>
    </Link>
  );
};

export default PivotlLogo;

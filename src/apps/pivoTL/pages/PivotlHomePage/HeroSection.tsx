import { Link } from 'react-router-dom';
import { bgGrid } from '../../assets/images';

export const HeroSection = () => {
  return (
    <section className="relative overflow-hidden">
      {/* Background Grid */}
      <div
        className="absolute inset-0 z-0"
        style={{
          backgroundImage: `url(${bgGrid})`,
          backgroundSize: 'auto',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
        }}
      />

      {/* Content */}
      <div className="relative z-10 mx-auto max-w-7xl px-4 py-24 sm:px-6 lg:px-8">
        <div className="text-center">
          <h1 className="mb-6 text-4xl font-semibold leading-normal text-gray-900 md:text-[56px]">
            The Agentic AI
            <br />
            Transformation Hub
          </h1>
          <p className="mx-auto mb-8 max-w-3xl font-inter text-lg text-gray-700">
            Orchestrate, evolve, and deploy AI agents across your enterprise.
            PivoTL is the transformation layer that powers agentic workflows,
            augments human roles, and accelerates value creation.
          </p>
          <div className="flex flex-col justify-center gap-4 px-4 sm:flex-row md:px-0">
            <Link
              to="/pivotl/agents/set-iq"
              className="rounded-sm bg-primary px-8 py-[9px] font-semibold text-white transition-colors hover:bg-orange-15"
            >
              Chat with Pivo
            </Link>
            <Link
              to="/pivotl/signup"
              className="rounded-sm border border-gray-700 px-6 py-2 font-medium text-gray-700 transition-colors hover:border-primary hover:bg-orange-50 hover:text-primary"
            >
              Regsiter for Free
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

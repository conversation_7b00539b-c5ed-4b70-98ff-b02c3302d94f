import { transformationLayers } from '../../assets/images';

export const PlatformSection = () => {
  return (
    <section className="-mt-10 bg-blue-midnight pt-16 font-inter text-white">
      <div className="text-center md:text-left">
        <div className="mb-10 px-6 md:mb-0 md:w-[55%] md:px-24">
          <h2 className="mb-4 text-3xl font-bold leading-normal md:mb-2.5 md:text-[40px] md:leading-tight">
            The Platform for Intelligent Value-Optimizing Transformation Layers
          </h2>
          <p className="mb-6 text-lg leading-9">
            PivoTL is a composable stack of autonomous AI layers—each designed
            to transform a specific business function, decision, or workflow
            with goal-aligned intelligence.
          </p>
        </div>

        <hr className="border-darkGray" />

        <div className="flex w-full flex-col items-center divide-darkGray md:flex-row md:divide-x-[1px]">
          <img
            src={transformationLayers}
            alt="Autonomous AI layers"
            className="w-full md:w-1/2 md:px-12"
          />
          <p className="w-full px-6 pb-10 text-lg leading-9 md:w-1/2 md:p-32 md:px-20">
            Just as the brain organizes perception, emotion, and action across
            interdependent layers of neurons, PiVoTL orchestrates transformation
            through coordinated layers of agentic intelligence. Each layer is
            built to sense, reason, and act with purpose—unlocking adaptive,
            outcome-optimized change across every level of your organization.
          </p>
        </div>
      </div>
    </section>
  );
};

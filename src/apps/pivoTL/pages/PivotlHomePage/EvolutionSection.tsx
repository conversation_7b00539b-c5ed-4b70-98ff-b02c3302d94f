import { starCircle } from '../../assets/images';
import { networkConnection } from '../../assets/videos';
import AutoPlayVideo from '@/shared/components/ui/AutoPlayVideo';

const features = [
  'Let us enable your AI transformation',
  'We’ll integrate your existing business stack',
  'From manager to CEO, we surface the insights that matter',
  'We accelerate our roadmap to fill gaps in your operation',
];

export const EvolutionSection = () => {
  return (
    <section className="pb-6 pt-10 font-inter">
      <div className="container mx-auto flex flex-col items-center px-6 md:flex-row">
        {/* Left Column */}
        <div className="py-6 md:w-1/2">
          <div className="mb-6 text-center md:w-4/5 md:px-4 md:text-left">
            <h2 className="mb-2 text-3xl font-semibold text-gray-20 md:mb-4 md:text-5xl">
              Always Evolving.
            </h2>
            <h2 className="text-3xl font-semibold text-blue-midnight md:text-5xl">
              What’s Coming Next?
            </h2>
            <p className="mt-4 text-lg">
              We’re building the AI ecosystem to transform how work happens
              across the enterprise.
            </p>
          </div>

          <div className="mt-4 space-y-4 md:p-4">
            {features.map((feature, index) => (
              <div key={index} className="flex items-center gap-3">
                <img
                  src={starCircle}
                  alt="✓"
                  className="h-5 w-5 flex-shrink-0"
                />
                <p className="text-sm">{feature}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Right Column - Video */}
        <div className="md:w-1/2">
          <AutoPlayVideo videoSrc={networkConnection} />
        </div>
      </div>
    </section>
  );
};

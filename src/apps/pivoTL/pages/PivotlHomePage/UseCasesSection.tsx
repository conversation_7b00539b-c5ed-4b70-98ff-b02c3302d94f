import {
  collections,
  salesOps,
  compliance,
  strategy,
} from '../../assets/images';

export const UseCasesSection = () => {
  const useCases = [
    {
      title: 'Collections',
      description: 'Increase recovery without friction',
      icon: collections,
    },
    {
      title: 'Sales Ops',
      description: 'Follow-up faster and smarter',
      icon: salesOps,
    },
    {
      title: 'Compliance',
      description: 'Surface risk before it spreads',
      icon: compliance,
    },
    {
      title: 'Strategy',
      description: 'Automate insights, track OKRs, stay adaptive',
      icon: strategy,
    },
  ];

  return (
    <section className="bg-greenThree pb-14 pt-20 font-inter">
      <div className="container mx-auto md:px-6">
        <div className="text-center">
          <h2 className="mb-4 text-center text-3xl font-bold leading-snug md:text-[40px]">
            Enterprise-Grade Use Cases.
            <br />
            Delivered by AI Agents
          </h2>
          <p className="mx-auto mb-6 text-center text-lg text-gray-600">
            From collections to strategy, PivoTL Agent Suites deliver precision,
            speed, and scale where it matters most.
          </p>
        </div>

        <div className="flex flex-wrap justify-center gap-8">
          {useCases.map((useCase, index) => (
            <div
              key={index}
              className="flex w-full max-w-72 items-center justify-center gap-6 rounded-sm border border-purpleOne bg-grayNineTeen px-6 py-4 transition md:w-[calc(50%-16px)] lg:w-[calc(25%-24px)]"
            >
              <img src={useCase.icon} alt={useCase.title} />
              <div>
                <h3 className="mb-2 text-xl font-semibold">{useCase.title}</h3>
                <p className="text-sm text-grayTen">{useCase.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

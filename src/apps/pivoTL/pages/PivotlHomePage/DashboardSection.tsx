import { clouds, dashboardImage, vectorGrid } from '../../assets/images';

export const DashboardSection = () => {
  return (
    <section className="relative -mt-12 overflow-hidden py-20 font-inter">
      {/* Background Cloud */}
      <div
        className="absolute inset-0 z-0 mt-16 h-[90%] md:mt-10"
        style={{
          backgroundImage: `url(${clouds})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
        }}
      />
      {/* Background Grid */}
      <div
        className="absolute inset-0 z-0 -mt-56 scale-150 opacity-30"
        style={{
          backgroundImage: `url(${vectorGrid})`,
          backgroundSize: 'auto',
          backgroundPosition: 'center',
          backgroundRepeat: 'repeat-x',
        }}
      />

      <div className="relative z-10 flex flex-col items-center md:flex-row">
        {/* Left Column */}
        <div className="relative py-6 md:w-1/2">
          <div className="md:w-4/5 md:pl-20">
            <div className="mb-8 px-4 text-center md:text-left">
              <h2 className="text-3xl font-semibold leading-snug text-blue-midnight md:text-[40px]">
                Purpose-Built Dashboards For Every AI Agents Suite
              </h2>
              <p className="mt-4 text-lg">
                Sales Operations AI Agents Accelerate pipelines with smart
                outreach, dynamic scoring, and seamless follow-up.
              </p>
            </div>

            <div className="text-darkGray">
              <div className="flex flex-col items-center md:flex-row">
                <div className="flex h-16 w-72 items-center justify-center border border-blueOne px-4 text-sm">
                  Real-time status of deployed agents
                </div>
                <div className="flex h-16 w-72 items-center justify-center border border-blueOne px-4 text-sm">
                  KPIs tracked per suite (e.g., resolution rate, lead
                  conversion, risk flags)
                </div>
              </div>

              <div className="flex flex-col items-center justify-center md:flex-row">
                <div className="flex h-16 w-72 items-center justify-center border-x border-blueOne px-4 text-sm">
                  Custom alerts, summaries, and exportable reports
                </div>
              </div>

              <div className="flex flex-col items-center md:flex-row">
                <div className="flex h-16 w-72 items-center justify-center border border-blueOne px-4 text-sm">
                  Agent usage breakdown with billing insights
                </div>
                <div className="flex h-16 w-72 items-center justify-center border border-blueOne px-4 text-sm">
                  Manager- and exec-level views by role or permission
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Right Column - Image */}
        <div className="px-2 md:w-1/2 md:px-0">
          <img
            src={dashboardImage}
            alt="Dashboard Image"
            className="h-full w-full rounded-sm border-2 border-grayTen object-contain md:border-4"
          />
        </div>
      </div>
    </section>
  );
};

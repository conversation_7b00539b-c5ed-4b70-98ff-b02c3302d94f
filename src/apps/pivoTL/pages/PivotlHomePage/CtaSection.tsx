import { Link } from 'react-router-dom';
import { activateAgent } from '../../assets/videos';
import AutoPlayVideo from '@/shared/components/ui/AutoPlayVideo';

export const CtaSection = () => {
  return (
    <section className="py-12 text-white">
      <div className="container mx-auto bg-blackOne px-4 py-12 md:px-14">
        <div className="px-4 text-center lg:px-8">
          <h2 className="mb-4 font-inter text-3xl font-bold md:text-5xl">
            Ready to Activate Your First AI Agent?
          </h2>
          <p className="mx-auto mb-8 max-w-2xl font-inter text-lg  text-gray-300">
            Start with one. Expand on your terms. Watch transformation compound.
          </p>

          <div className="flex flex-col justify-center gap-6 sm:flex-row">
            <Link
              to="/pivotl/agents"
              className="rounded-sm bg-primary px-8 py-[9px] font-medium text-white transition-colors hover:bg-orange-15"
            >
              Browse Agents
            </Link>
            <Link
              to="/pivotl/demo"
              className="rounded-sm border border-white px-6 py-2 font-medium transition-colors hover:border-primary hover:bg-orange-50 hover:text-primary"
            >
              Schedule a Demo
            </Link>
          </div>
        </div>

        <div className="mt-8">
          <AutoPlayVideo videoSrc={activateAgent} />
        </div>
      </div>
    </section>
  );
};

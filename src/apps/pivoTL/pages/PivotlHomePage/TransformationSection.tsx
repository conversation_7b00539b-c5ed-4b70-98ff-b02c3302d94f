import { checkCircle, enterpriseLayers } from '../../assets/images';

const features = [
  'Subscription billing with usage-based overage tracking',
  'Low-latency orchestration logic built for real-time response',
  'Seamless integration with your existing business stack',
  'Scalable deployment across departments and workflows',
  'Built-in usage insights, access control, and audit trails',
  'Intelligent routing, retry logic, and policy enforcement for every task',
];

export const TransformationSection = () => {
  return (
    <section className="bg-lightOrangeTwo font-inter text-darkGray md:py-12">
      <div className="flex flex-col md:flex-row">
        {/* Left Column - Border Container */}
        <div className="relative md:w-1/2">
          <div className="border md:absolute md:inset-y-0 md:left-0 md:ml-28 md:w-4/5 md:border-gray-300">
            <div className="p-4 md:p-0">
              <div className="mb-8 px-4 pt-8 text-center md:max-w-[470px] md:px-6 md:text-left">
                <h2 className="text-[28px] font-bold leading-tight text-blue-midnight md:text-[32px]">
                  The Transformation Layer Your Enterprise Is Missing
                </h2>
                <p className="mt-2 text-lg">
                  Across every agent, PivoTL coordinates decisions, tasks, data,
                  and actions through a secure, orchestrated infrastructure.
                </p>
              </div>

              <hr />

              <div className="space-y-4 p-4 pt-6">
                {features.map((feature, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <img
                      src={checkCircle}
                      alt="✓"
                      className="h-5 w-5 flex-shrink-0"
                    />
                    <p className="text-sm">{feature}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Right Column - Image */}
        <div className="md:w-1/2">
          <img
            src={enterpriseLayers}
            alt="Transformation Layer Diagram"
            className="h-full w-full object-contain"
          />
        </div>
      </div>
    </section>
  );
};

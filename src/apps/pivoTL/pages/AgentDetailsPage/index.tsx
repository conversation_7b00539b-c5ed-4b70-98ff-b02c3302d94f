import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { agents, featureIcons, marketplaceAgents } from '../../data/constants';
import { ChevronRight } from 'lucide-react';
import { useRef, useState } from 'react';
import { useOnClickOutside } from '@/hooks/useOnClickOutside';
import { useChatApi } from '../../hooks/useChatApi';
import { OnboardingProgressSidebar } from '../../components/sidebar/OnboardingProgressSidebar';
import { MarketplaceAgentsSidebar } from '../../components/sidebar/MarketplaceAgentsSidebar';
import { ConversationalChatInterface } from '../../components/chat/ConversationalChatInterface';

const AgentDetailsPage = () => {
  const { agentId } = useParams<{ agentId: string }>();
  const agent = agentId
    ? agents.filter(agent => agent.id === agentId)[0]
    : null;
  const [activeAgent, setActiveAgent] = useState(marketplaceAgents[0]);
  const [showAgentFeatures, setShowAgentFeatures] = useState(false);
  const agentFeaturesRef = useRef<HTMLDivElement>(null);
  const toggleAgentFeatures = () => setShowAgentFeatures(previous => !previous);

  useOnClickOutside(agentFeaturesRef, () => setShowAgentFeatures(false));

  const {
    state,
    sendMessage,
    handleSignupStart,
    handleResendVerificationCode,
  } = useChatApi();

  const showOnboardingProgress = state.onboardingProgress.isSignupStarted;

  if (!agent) {
    return (
      <div className="-mt-20 flex min-h-screen items-center justify-center bg-gray-50">
        <div className="text-center">
          <h1 className="mb-4 text-2xl font-bold text-gray-900">
            Agent Not Found
          </h1>
          <p className="mb-6 text-gray-600">
            The agent you're looking for doesn't exist.
          </p>
          <Link
            to="/pivotl"
            className="rounded-lg bg-primary px-6 py-3 font-medium text-white transition-colors hover:bg-orange-15"
          >
            Back to Home
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <div className="mx-auto max-w-7xl p-4 lg:px-8">
        <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
          {/* --- Main Content --- */}
          <div className="lg:col-span-2">
            {/* Agent Header */}
            <div>
              <div
                className="h-[168px] rounded-t-lg border bg-gray-200 bg-cover bg-center"
                style={{ backgroundImage: `url(${agent.image})` }}
              >
                <h1 className="ml-6 mt-6 w-fit rounded bg-white px-4 py-2 text-[32px] font-bold">
                  {agent.name}
                </h1>
              </div>
              <div className="py-4">
                <h2 className="mb-1.5 text-2xl font-semibold">
                  {agent.category}
                </h2>
                <p className="font-inter text-lg">{agent.description}</p>
              </div>
            </div>

            {/* Active Marketplace agent */}
            {state.messages && state.messages.length === 0 && (
              <div className="relative w-[90%]">
                <div
                  ref={agentFeaturesRef}
                  className="mb-4 flex cursor-pointer items-center gap-4 rounded-lg"
                  onClick={toggleAgentFeatures}
                >
                  <div className="h-12 rounded-full bg-peachTwo">
                    <img
                      src={activeAgent.image}
                      className="h-[95%] object-cover"
                      alt={activeAgent.name}
                    />
                  </div>
                  <div className="flex items-center gap-1">
                    <p className="text-xl font-semibold text-darkGray">
                      {activeAgent.name}
                    </p>
                    <ChevronRight
                      className={`${
                        showAgentFeatures ? 'rotate-90' : 'rotate-0'
                      }`}
                    />
                  </div>
                </div>

                {/* Marketplace agent features */}
                {showAgentFeatures && (
                  <div className="mb-4 flex h-[120px] w-full items-center gap-4 rounded-lg border border-peachTwo">
                    <div className="h-full rounded-l-lg bg-peachTwo">
                      <img
                        src={activeAgent.image}
                        className="h-[95%] object-cover"
                        alt={activeAgent.name}
                      />
                    </div>
                    <div className="flex flex-col gap-4">
                      {activeAgent.features.map((feature, index) => (
                        <div key={index} className="flex items-center gap-3">
                          <img
                            src={featureIcons[index % 3]}
                            alt=""
                            className="w-5"
                          />
                          <p className="text-sm text-blackTwo">{feature}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            <div
              className={`${state.messages && state.messages.length === 0 ? 'h-2/3' : 'h-[600px]'} bg-white font-inter`}
            >
              <ConversationalChatInterface
                state={state}
                sendMessage={sendMessage}
                handleSignupStart={handleSignupStart}
              />
            </div>
          </div>

          {/* --- Sidebar --- */}
          <div className="lg:col-span-1">
            {showOnboardingProgress ? (
              <OnboardingProgressSidebar
                onboardingProgress={state.onboardingProgress}
                onResendVerificationCode={handleResendVerificationCode}
              />
            ) : (
              <MarketplaceAgentsSidebar
                activeAgent={activeAgent}
                onAgentSelect={setActiveAgent}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AgentDetailsPage;

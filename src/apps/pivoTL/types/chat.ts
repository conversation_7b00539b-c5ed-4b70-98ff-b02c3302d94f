export type MessageSender = 'user' | 'scyra';

export interface ChatMessage {
  id: string;
  sender: MessageSender;
  content: string;
  timestamp: Date;
  senderName: string;
  senderIcon?: string;
}

export interface OnboardingProgress {
  firstName?: string;
  lastName?: string;
  email?: string;
  verificationCode?: string;
  password?: string;
  emailVerified?: boolean;
  isSignupStarted: boolean;
  isSignupCompleted: boolean;
  currentStep: OnboardingStep;
}

export type OnboardingStep =
  | 'initial'
  | 'firstName'
  | 'lastName'
  | 'email'
  | 'verification'
  | 'password'
  | 'completed';

export interface ConversationState {
  messages: ChatMessage[];
  onboardingProgress: OnboardingProgress;
  isLoading: boolean;
  showSignupButton: boolean;
  showProceedToPivoButton: boolean;
  sessionId: string;
}

export interface PasswordValidation {
  minLength: boolean;
  hasLowercase: boolean;
  hasUppercase: boolean;
  hasDigit: boolean;
  hasSpecialChar: boolean;
}

export interface SidebarState {
  showMarketplaceAgents: boolean;
  showOnboardingFields: boolean;
  onboardingData: OnboardingProgress;
}

// API Types
export interface ChatApiRequest {
  userMessage: string;
}

export interface ChatApiResponse {
  message: string;
}

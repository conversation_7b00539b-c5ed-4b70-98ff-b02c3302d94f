import { useState, useCallback } from 'react';
import {
  ConversationState,
  ChatMessage,
  OnboardingProgress,
  OnboardingStep,
  MessageSender,
} from '../types/chat';

const initialOnboardingProgress: OnboardingProgress = {
  isSignupStarted: false,
  isSignupCompleted: false,
  currentStep: 'initial',
};

const initialState: ConversationState = {
  messages: [],
  onboardingProgress: initialOnboardingProgress,
  isLoading: false,
  showSignupButton: false,
  showProceedToPivoButton: false,
};

export const useConversationState = () => {
  const [state, setState] = useState<ConversationState>(initialState);

  const addMessage = useCallback(
    (sender: MessageSender, content: string, senderName: string) => {
      const newMessage: ChatMessage = {
        id: Date.now().toString(),
        sender,
        content,
        timestamp: new Date(),
        senderName,
      };

      setState(prev => ({
        ...prev,
        messages: [...prev.messages, newMessage],
        // Show signup button only after Scyra messages (not user messages)
        showSignupButton:
          sender === 'scyra' && !prev.onboardingProgress.isSignupStarted,
      }));
    },
    [],
  );

  const setLoading = useCallback((loading: boolean) => {
    setState(prev => ({ ...prev, isLoading: loading }));
  }, []);

  const startSignup = useCallback(() => {
    setState(prev => ({
      ...prev,
      onboardingProgress: {
        ...prev.onboardingProgress,
        isSignupStarted: true,
        currentStep: 'firstName',
      },
      showSignupButton: false,
    }));
  }, []);

  const updateOnboardingProgress = useCallback(
    (
      field: keyof OnboardingProgress,
      value: string | boolean | OnboardingStep,
    ) => {
      setState(prev => ({
        ...prev,
        onboardingProgress: {
          ...prev.onboardingProgress,
          [field]: value,
        },
      }));
    },
    [],
  );

  const extractOnboardingDataFromMessage = useCallback(
    (message: string) => {
      // Extract user data from Scyra responses based on conversation context
      const lowerMessage = message.toLowerCase();

      if (
        lowerMessage.includes('first name') ||
        lowerMessage.includes('what is your first name')
      ) {
        updateOnboardingProgress('currentStep', 'firstName');
      } else if (
        lowerMessage.includes('last name') ||
        lowerMessage.includes('what is your last name')
      ) {
        updateOnboardingProgress('currentStep', 'lastName');
      } else if (
        lowerMessage.includes('email') ||
        lowerMessage.includes('what is your email')
      ) {
        updateOnboardingProgress('currentStep', 'email');
      } else if (
        lowerMessage.includes('verification code') ||
        lowerMessage.includes('enter the code')
      ) {
        updateOnboardingProgress('currentStep', 'verification');
      } else if (
        lowerMessage.includes('password') ||
        lowerMessage.includes('enter your password')
      ) {
        updateOnboardingProgress('currentStep', 'password');
      } else if (
        lowerMessage.includes('verification completed') ||
        lowerMessage.includes('pass you over to pivo')
      ) {
        setState(prev => ({
          ...prev,
          onboardingProgress: {
            ...prev.onboardingProgress,
            isSignupCompleted: true,
            currentStep: 'completed',
          },
          showProceedToPivoButton: true,
        }));
      }
    },
    [updateOnboardingProgress],
  );

  const extractUserDataFromUserMessage = useCallback(
    (message: string, currentStep: OnboardingStep) => {
      // Extract and store user input based on current step
      switch (currentStep) {
        case 'firstName':
          updateOnboardingProgress('firstName', message);
          break;
        case 'lastName':
          updateOnboardingProgress('lastName', message);
          break;
        case 'email':
          updateOnboardingProgress('email', message);
          break;
        case 'verification':
          updateOnboardingProgress('verificationCode', message);
          break;
        case 'password':
          updateOnboardingProgress('password', message);
          break;
      }
    },
    [updateOnboardingProgress],
  );

  const resetConversation = useCallback(() => {
    setState(initialState);
  }, []);

  return {
    state,
    addMessage,
    setLoading,
    startSignup,
    updateOnboardingProgress,
    extractOnboardingDataFromMessage,
    extractUserDataFromUserMessage,
    resetConversation,
  };
};

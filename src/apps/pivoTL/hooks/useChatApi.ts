import { useCallback } from 'react';
import { useChatWithScyraApi } from '../services/upivotalIntegrationService';
import { useConversationState } from './useConversationState';

export const useChatApi = () => {
  const chatWithScyra = useChatWithScyraApi();
  const {
    state,
    addMessage,
    setLoading,
    startSignup,
    extractOnboardingDataFromMessage,
    extractUserDataFromUserMessage,
  } = useConversationState();

  const sendMessage = useCallback(
    async (userMessage: string) => {
      try {
        // Add user message immediately
        addMessage('user', userMessage, 'You');

        // Extract user data if we're in onboarding flow
        if (state.onboardingProgress.isSignupStarted) {
          extractUserDataFromUserMessage(
            userMessage,
            state.onboardingProgress.currentStep,
          );
        }

        // Set loading state
        setLoading(true);

        // Send message to API
        const response = await chatWithScyra({ userMessage });

        // Add Scyra response
        addMessage('scyra', response, 'Scyra');

        // Extract onboarding data from <PERSON><PERSON>'s response
        extractOnboardingDataFromMessage(response);
      } catch (error) {
        console.error('Chat error:', error);
        addMessage(
          'scyra',
          'I apologize, but I encountered an error. Please try again.',
          'Scyra',
        );
      } finally {
        setLoading(false);
      }
    },
    [
      addMessage,
      setLoading,
      chatWithScyra,
      state.onboardingProgress.isSignupStarted,
      state.onboardingProgress.currentStep,
      extractOnboardingDataFromMessage,
      extractUserDataFromUserMessage,
    ],
  );

  const handleSignupStart = useCallback(async () => {
    startSignup();
    await sendMessage('signup');
  }, [startSignup, sendMessage]);

  const handleResendVerificationCode = useCallback(async () => {
    await sendMessage('I did not get the verification code, please resend it');
  }, [sendMessage]);

  return {
    state,
    sendMessage,
    handleSignupStart,
    handleResendVerificationCode,
  };
};

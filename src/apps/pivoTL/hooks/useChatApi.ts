import { useCallback } from 'react';
import { useChatWithScyraApi } from '../services/upivotalIntegrationService';
import { useConversationState } from './useConversationState';

export const useChatApi = () => {
  const chatWithScyra = useChatWithScyraApi();
  const {
    state,
    addMessage,
    setLoading,
    startSignup,
    extractOnboardingDataFromMessage,
    extractUserDataFromUserMessage,
  } = useConversationState();

  const sendMessage = useCallback(
    async (userMessage: string) => {
      try {
        // Add user message immediately
        addMessage('user', userMessage, 'You');

        // Extract user data if we're in onboarding flow
        if (state.onboardingProgress.isSignupStarted) {
          extractUserDataFromUserMessage(
            userMessage,
            state.onboardingProgress.currentStep,
          );
        }

        // Set loading state
        setLoading(true);

        // Send message to API
        const response = await chatWithScyra({
          userMessage,
          sessionId: state.sessionId,
        });

        console.log('scyra response', response);

        // Extract onboarding data from <PERSON><PERSON>'s full response (including metadata)
        extractOnboardingDataFromMessage(response);

        // Add Scyra response (display text only, without metadata)
        const displayMessage = response.split('metaData')[0].trim();
        addMessage('scyra', displayMessage, 'Scyra');
      } catch (error) {
        console.error('Chat error:', error);
        addMessage(
          'scyra',
          'I apologize, but I encountered an error. Please try again.',
          'Scyra',
        );
      } finally {
        setLoading(false);
      }
    },
    [
      addMessage,
      setLoading,
      chatWithScyra,
      state.onboardingProgress.isSignupStarted,
      state.onboardingProgress.currentStep,
      extractOnboardingDataFromMessage,
      extractUserDataFromUserMessage,
    ],
  );

  const handleSignupStart = useCallback(async () => {
    startSignup();
    await sendMessage('I would like to signup');
  }, [startSignup, sendMessage]);

  const handleResendVerificationCode = useCallback(async () => {
    await sendMessage('I did not get the verification code, please resend it');
  }, [sendMessage]);

  const handleSubmitVerificationCode = useCallback(
    async (code: string) => {
      // Validate that the code is exactly 6 digits and contains only numbers
      if (code.length === 6 && /^\d{6}$/.test(code)) {
        await sendMessage(code);
      }
    },
    [sendMessage],
  );

  const handleSubmitPassword = useCallback(
    async (password: string) => {
      await sendMessage(password);
    },
    [sendMessage],
  );

  return {
    state,
    sendMessage,
    handleSignupStart,
    handleResendVerificationCode,
    handleSubmitVerificationCode,
    handleSubmitPassword,
  };
};

import { publicRequest } from '../../../lib/axios/publicRequest';
import { BASE_URL } from '../../../utils/apiUrls';
import { upivotalIntegration } from '../../../utils/apiServiceControllersRoute';

/**
 * Generates a secure session ID using Web Crypto API
 * @returns A 64-character hex string representing a secure session ID
 */
export const generateSecureSessionId = (): string => {
  // Generate 32 random bytes (256 bits) for strong security
  const array = new Uint8Array(32);
  crypto.getRandomValues(array);

  // Convert to hex string
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
};

export interface ChatRequest {
  userMessage: string;
  sessionId: string;
}

export interface ChatResponse {
  message: string;
}

export const useChatWithScyraApi = () => {
  const chatWithScyra = async (payload: ChatRequest): Promise<string> => {
    try {
      const res = await publicRequest(BASE_URL)?.post(
        `${upivotalIntegration}/ai/scyra/chat`,
        payload,
      );

      // API returns plain text format
      return res?.data || '';
    } catch (error) {
      console.error('Chat API Error:', error);
      throw new Error('Failed to communicate with Scyra. Please try again.');
    }
  };

  return chatWithScyra;
};

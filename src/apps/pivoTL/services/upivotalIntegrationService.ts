import { publicRequest } from '../../../lib/axios/publicRequest';
import { BASE_URL } from '../../../utils/apiUrls';
import { upivotalIntegration } from '../../../utils/apiServiceControllersRoute';

export interface ChatRequest {
  userMessage: string;
}

export interface ChatResponse {
  message: string;
}

export const useChatWithScyraApi = () => {
  const chatWithScyra = async (payload: ChatRequest): Promise<string> => {
    try {
      const res = await publicRequest(BASE_URL)?.post(
        `${upivotalIntegration}/ai/scyra/chat`,
        {
          ...payload,
          sessionId:
            'a1b2c3d4e5f67890a1b2c3d4e5f67890a1b2c3d4e5f67890a1b2c3d4e5f67890',
        },
      );

      // API returns plain text format
      return res?.data || '';
    } catch (error) {
      console.error('Chat API Error:', error);
      throw new Error('Failed to communicate with <PERSON><PERSON>. Please try again.');
    }
  };

  return chatWithScyra;
};
